@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
}

.App {
  text-align: left;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Animation classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.5s ease-out forwards;
}

.animate-bounceIn {
  animation: bounceIn 0.8s ease-out forwards;
}

/* Custom gradient backgrounds */
.gradient-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.gradient-orange {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button styles */
.btn-primary {
  @apply bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transform hover:scale-105 transition duration-300 shadow-lg;
}

.btn-secondary {
  @apply bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold border-2 border-blue-600 hover:bg-blue-600 hover:text-white transition duration-300;
}

.btn-outline {
  @apply border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition duration-300;
}

/* Form styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Loading spinner */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* Status badges */
.status-available {
  @apply bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold;
}

.status-busy {
  @apply bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-semibold;
}

/* Hero section styles */
.hero-overlay {
  background: linear-gradient(
    45deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

/* Service card styles */
.service-card {
  @apply bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition duration-300 border border-gray-100;
}

.service-price {
  @apply text-2xl font-bold text-blue-600;
}

/* Portfolio gallery */
.portfolio-image {
  @apply rounded-lg overflow-hidden shadow-md hover:shadow-lg transition duration-300 cursor-pointer;
}

.portfolio-image:hover {
  transform: scale(1.05);
}

/* Rating stars */
.star-filled {
  @apply text-yellow-400 fill-current;
}

.star-empty {
  @apply text-gray-300 fill-current;
}

/* Responsive design helpers */
@media (max-width: 640px) {
  .container-mobile {
    @apply px-4;
  }
  
  .text-responsive {
    @apply text-sm;
  }
  
  .btn-mobile {
    @apply w-full text-center;
  }
}

/* Custom utility classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, #667eea, #764ba2) border-box;
}

/* Barber specialties tags */
.specialty-tag {
  @apply inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-2 mb-2;
}

/* Booking form styles */
.booking-form {
  @apply bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-screen overflow-y-auto;
}

.booking-form-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

/* Success/Error message styles */
.message-success {
  @apply bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded;
}

.message-error {
  @apply bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded;
}

.message-warning {
  @apply bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded;
}

/* Navigation styles */
.nav-link {
  @apply text-gray-900 hover:text-blue-600 transition duration-300 font-medium;
}

.nav-link-active {
  @apply text-blue-600 border-b-2 border-blue-600;
}

/* Footer styles */
.footer-link {
  @apply text-gray-400 hover:text-white transition duration-300;
}

/* Image effects */
.image-zoom {
  transition: transform 0.3s ease;
}

.image-zoom:hover {
  transform: scale(1.1);
}

/* Custom backgrounds */
.bg-pattern {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

.bg-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* Accessibility improvements */
.focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Dark mode support (if needed later) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }
}