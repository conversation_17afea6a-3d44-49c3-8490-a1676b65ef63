from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON><PERSON>, HTTPException, Request, Depends
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timedelta
from emergentintegrations.payments.stripe.checkout import <PERSON>e<PERSON>heck<PERSON>, CheckoutSessionResponse, CheckoutStatusResponse, CheckoutSessionRequest
import socketio
import asyncio

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# Create the main app without a prefix
app = FastAPI()

# Socket.IO setup for real-time features
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True
)
socket_app = socketio.ASGIApp(sio, app)

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Stripe setup
stripe_api_key = os.environ.get('STRIPE_API_KEY')

# Define Models
class Customer(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    email: str
    phone: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

class CustomerCreate(BaseModel):
    name: str
    email: str
    phone: str

class Service(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    price: float
    duration: int  # in minutes
    description: str

class WorkingHours(BaseModel):
    start: str
    end: str
    break_start: Optional[str] = None
    break_end: Optional[str] = None
    is_working: bool = True

class Location(BaseModel):
    address: str
    city: str
    state: str
    postal_code: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None

class Barber(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    shop_name: Optional[str] = None
    email: str
    phone: str
    bio: str
    specialties: List[str]
    certifications: List[str] = []
    languages: List[str] = []
    rating: float = 4.5
    total_reviews: int = 0
    profile_image: str
    portfolio_images: List[str] = []
    services: List[Service] = []
    working_hours: Dict[str, WorkingHours] = {}
    location: Optional[Location] = None
    is_available: bool = True
    max_bookings_per_slot: int = 1
    accepts_walk_ins: bool = True
    pricing_type: str = "fixed"  # fixed, dynamic
    peak_hours_multiplier: float = 1.0
    discount_codes: List[Dict[str, Any]] = []
    payment_modes: List[str] = ["online", "cash"]
    queue_position: int = 0
    current_queue_size: int = 0
    average_service_time: int = 30
    created_at: datetime = Field(default_factory=datetime.utcnow)

class BarberCreate(BaseModel):
    name: str
    shop_name: Optional[str] = None
    email: str
    phone: str
    bio: str
    specialties: List[str]
    certifications: List[str] = []
    languages: List[str] = []
    profile_image: str
    portfolio_images: List[str] = []
    services: List[Service] = []
    location: Optional[Location] = None

class BarberRegistration(BaseModel):
    name: str
    shop_name: Optional[str] = None
    email: str
    phone: str
    address: str
    city: str
    state: str
    postal_code: str
    bio: str
    experience_years: int
    specialties: List[str]
    languages: List[str] = []
    certifications: List[str] = []

class Booking(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    customer_id: str
    barber_id: str
    service_id: str
    appointment_date: datetime
    status: str = "pending"  # pending, confirmed, in_progress, completed, cancelled
    total_amount: float
    notes: str = ""
    payment_status: str = "pending"  # pending, paid, failed
    payment_session_id: Optional[str] = None
    queue_position: int = 0
    estimated_wait_time: int = 0  # in minutes
    is_walk_in: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)

class BookingCreate(BaseModel):
    customer_id: str
    barber_id: str
    service_id: str
    appointment_date: datetime
    notes: str = ""
    is_walk_in: bool = False

class PaymentTransaction(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    payment_id: Optional[str] = None
    booking_id: str
    customer_id: str
    amount: float
    currency: str = "inr"  # Changed to INR
    payment_status: str = "initiated"  # initiated, pending, paid, failed, expired
    status: str = "initiated"
    metadata: Optional[Dict[str, str]] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class Review(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    customer_id: str
    barber_id: str
    booking_id: str
    rating: int = Field(ge=1, le=5)
    comment: str = ""
    response: Optional[str] = None  # Barber's response
    created_at: datetime = Field(default_factory=datetime.utcnow)

class QueueUpdate(BaseModel):
    barber_id: str
    queue_size: int
    estimated_wait_time: int
    current_customer: Optional[str] = None

# Queue management system
active_queues = {}  # barber_id -> queue info

# Socket.IO Events
@sio.event
async def connect(sid, environ):
    print(f"Client {sid} connected")
    await sio.emit('connected', {'message': 'Connected to TrimTime real-time updates'}, room=sid)

@sio.event
async def disconnect(sid):
    print(f"Client {sid} disconnected")

@sio.event
async def join_barber_queue(sid, data):
    """Client joins a specific barber's queue updates"""
    barber_id = data.get('barber_id')
    if barber_id:
        await sio.enter_room(sid, f"barber_{barber_id}")
        queue_info = active_queues.get(barber_id, {"size": 0, "wait_time": 0})
        await sio.emit('queue_update', queue_info, room=sid)

@sio.event
async def leave_barber_queue(sid, data):
    """Client leaves barber's queue updates"""
    barber_id = data.get('barber_id')
    if barber_id:
        await sio.leave_room(sid, f"barber_{barber_id}")

# Helper function to broadcast queue updates
async def broadcast_queue_update(barber_id: str, queue_size: int, wait_time: int):
    active_queues[barber_id] = {"size": queue_size, "wait_time": wait_time}
    await sio.emit('queue_update', {
        'barber_id': barber_id,
        'queue_size': queue_size,
        'estimated_wait_time': wait_time
    }, room=f"barber_{barber_id}")

# Initialize demo barbers with Indian context
async def create_demo_barbers():
    existing_barbers = await db.barbers.count_documents({})
    if existing_barbers == 0:
        demo_barbers = [
            {
                "id": str(uuid.uuid4()),
                "name": "Raj Sharma",
                "shop_name": "Classic Cuts Salon",
                "email": "<EMAIL>",
                "phone": "+91-98765-43210",
                "bio": "Master barber with 15+ years experience. Specializes in modern fades and traditional Indian cuts.",
                "specialties": ["Modern Fades", "Traditional Cuts", "Beard Styling", "Wedding Prep"],
                "certifications": ["Advanced Hair Styling Certificate", "Beard Specialist"],
                "languages": ["Hindi", "English", "Punjabi"],
                "rating": 4.8,
                "total_reviews": 127,
                "profile_image": "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NDk1NzZ8MHwxfHNlYXJjaHwxfHxiYXJiZXJzaG9wfGVufDB8fHx8MTc1NDgyMDcxM3ww&ixlib=rb-4.1.0&q=85",
                "portfolio_images": [
                    "https://images.unsplash.com/photo-1593702275687-f8b402bf1fb5?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTY2NzF8MHwxfHNlYXJjaHwxfHxoYWlyY3V0fGVufDB8fHx8MTc1NDgyMDcxOXww&ixlib=rb-4.1.0&q=85",
                    "https://images.unsplash.com/photo-1605497788044-5a32c7078486?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTY2NzF8MHwxfHNlYXJjaHwyfHxoYWlyY3V0fGVufDB8fHx8MTc1NDgyMDcxOXww&ixlib=rb-4.1.0&q=85"
                ],
                "services": [
                    {"id": str(uuid.uuid4()), "name": "Basic Haircut", "price": 250.0, "duration": 30, "description": "Clean, professional haircut"},
                    {"id": str(uuid.uuid4()), "name": "Premium Cut & Style", "price": 450.0, "duration": 45, "description": "Haircut with styling and finish"},
                    {"id": str(uuid.uuid4()), "name": "Beard Trim", "price": 150.0, "duration": 20, "description": "Professional beard trimming and shaping"},
                    {"id": str(uuid.uuid4()), "name": "Wedding Special", "price": 1200.0, "duration": 90, "description": "Complete grooming package for special occasions"}
                ],
                "working_hours": {
                    "monday": {"start": "09:00", "end": "20:00", "break_start": "13:00", "break_end": "14:00", "is_working": True},
                    "tuesday": {"start": "09:00", "end": "20:00", "break_start": "13:00", "break_end": "14:00", "is_working": True},
                    "wednesday": {"start": "09:00", "end": "20:00", "break_start": "13:00", "break_end": "14:00", "is_working": True},
                    "thursday": {"start": "09:00", "end": "20:00", "break_start": "13:00", "break_end": "14:00", "is_working": True},
                    "friday": {"start": "09:00", "end": "21:00", "break_start": "13:00", "break_end": "14:00", "is_working": True},
                    "saturday": {"start": "08:00", "end": "21:00", "break_start": "13:00", "break_end": "14:00", "is_working": True},
                    "sunday": {"start": "10:00", "end": "18:00", "is_working": True}
                },
                "location": {
                    "address": "Shop No. 12, Connaught Place",
                    "city": "New Delhi",
                    "state": "Delhi",
                    "postal_code": "110001",
                    "latitude": 28.6315,
                    "longitude": 77.2167
                },
                "is_available": True,
                "max_bookings_per_slot": 2,
                "accepts_walk_ins": True,
                "payment_modes": ["online", "cash", "upi"],
                "current_queue_size": 2,
                "average_service_time": 35,
                "created_at": datetime.utcnow()
            },
            {
                "id": str(uuid.uuid4()),
                "name": "David D'Souza", 
                "shop_name": "Urban Cuts Studio",
                "email": "<EMAIL>",
                "phone": "+91-98876-54321",
                "bio": "Award-winning barber specializing in contemporary styles and precision cuts. Expert in international trends.",
                "specialties": ["Contemporary Styles", "Precision Cuts", "Hair Coloring", "Texture Styling"],
                "certifications": ["International Hair Academy", "Color Specialist"],
                "languages": ["English", "Hindi", "Marathi"],
                "rating": 4.7,
                "total_reviews": 89,
                "profile_image": "https://images.unsplash.com/photo-1621645582931-d1d3e6564943?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NDk1NzZ8MHwxfHNlYXJjaHwyfHxiYXJiZXJzaG9wfGVufDB8fHx8MTc1NDgyMDcxM3ww&ixlib=rb-4.1.0&q=85",
                "portfolio_images": [
                    "https://images.unsplash.com/photo-1647140655214-e4a2d914971f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NDk1NzZ8MHwxfHNlYXJjaHwzfHxiYXJiZXJzaG9wfGVufDB8fHx8MTc1NDgyMDcxM3ww&ixlib=rb-4.1.0&q=85",
                    "https://images.unsplash.com/photo-1585747860715-2ba37e788b70?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NDk1NzZ8MHwxfHNlYXJjaHw0fHxiYXJiZXJzaG9wfGVufDB8fHx8MTc1NDgyMDcxM3ww&ixlib=rb-4.1.0&q=85"
                ],
                "services": [
                    {"id": str(uuid.uuid4()), "name": "Executive Cut", "price": 350.0, "duration": 40, "description": "Professional cut for business professionals"},
                    {"id": str(uuid.uuid4()), "name": "Style Makeover", "price": 650.0, "duration": 60, "description": "Complete style transformation"},
                    {"id": str(uuid.uuid4()), "name": "Color & Cut", "price": 1500.0, "duration": 120, "description": "Hair coloring with precision cut"}
                ],
                "working_hours": {
                    "monday": {"start": "10:00", "end": "19:00", "is_working": True},
                    "tuesday": {"start": "10:00", "end": "19:00", "is_working": True},
                    "wednesday": {"start": "10:00", "end": "19:00", "is_working": True},
                    "thursday": {"start": "10:00", "end": "19:00", "is_working": True},
                    "friday": {"start": "10:00", "end": "20:00", "is_working": True},
                    "saturday": {"start": "09:00", "end": "20:00", "is_working": True},
                    "sunday": {"start": "11:00", "end": "17:00", "is_working": True}
                },
                "location": {
                    "address": "2nd Floor, Phoenix Mall, Lower Parel",
                    "city": "Mumbai",
                    "state": "Maharashtra", 
                    "postal_code": "400013",
                    "latitude": 19.0176,
                    "longitude": 72.8562
                },
                "is_available": True,
                "max_bookings_per_slot": 1,
                "accepts_walk_ins": False,
                "payment_modes": ["online", "card"],
                "current_queue_size": 0,
                "average_service_time": 45,
                "created_at": datetime.utcnow()
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Mohammed Khan",
                "shop_name": "Heritage Barbershop",
                "email": "<EMAIL>",
                "phone": "+91-99887-76543",
                "bio": "Traditional barber with modern techniques. Master of classic styles and luxury grooming experiences.",
                "specialties": ["Classic Styles", "Hot Towel Shaves", "Traditional Cuts", "Luxury Grooming"],
                "certifications": ["Traditional Barbering Master", "Hot Towel Specialist"],
                "languages": ["Urdu", "Hindi", "English"],
                "rating": 4.9,
                "total_reviews": 156,
                "profile_image": "https://images.pexels.com/photos/1570806/pexels-photo-1570806.jpeg",
                "portfolio_images": [
                    "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NDk1NzZ8MHwxfHNlYXJjaHwxfHxiYXJiZXJzaG9wfGVufDB8fHx8MTc1NDgyMDcxM3ww&ixlib=rb-4.1.0&q=85"
                ],
                "services": [
                    {"id": str(uuid.uuid4()), "name": "Traditional Cut", "price": 300.0, "duration": 35, "description": "Classic barbering with traditional techniques"},
                    {"id": str(uuid.uuid4()), "name": "Royal Shave", "price": 400.0, "duration": 45, "description": "Luxury hot towel shave experience"},
                    {"id": str(uuid.uuid4()), "name": "Maharaja Package", "price": 1500.0, "duration": 90, "description": "Complete royal grooming experience"}
                ],
                "working_hours": {
                    "monday": {"start": "10:00", "end": "21:00", "break_start": "14:00", "break_end": "15:00", "is_working": True},
                    "tuesday": {"start": "10:00", "end": "21:00", "break_start": "14:00", "break_end": "15:00", "is_working": True},
                    "wednesday": {"start": "10:00", "end": "21:00", "break_start": "14:00", "break_end": "15:00", "is_working": True},
                    "thursday": {"start": "10:00", "end": "21:00", "break_start": "14:00", "break_end": "15:00", "is_working": True},
                    "friday": {"start": "14:00", "end": "22:00", "is_working": True},
                    "saturday": {"start": "09:00", "end": "22:00", "is_working": True},
                    "sunday": {"start": "12:00", "end": "20:00", "is_working": True}
                },
                "location": {
                    "address": "Old City, Charminar Area",
                    "city": "Hyderabad",
                    "state": "Telangana",
                    "postal_code": "500002",
                    "latitude": 17.3616,
                    "longitude": 78.4747
                },
                "is_available": True,
                "max_bookings_per_slot": 1,
                "accepts_walk_ins": True,
                "payment_modes": ["online", "cash"],
                "current_queue_size": 1,
                "average_service_time": 40,
                "created_at": datetime.utcnow()
            }
        ]
        
        await db.barbers.insert_many(demo_barbers)
        logger.info("Demo barbers created successfully")

# API Routes
@api_router.get("/")
async def root():
    return {"message": "TrimTime API - Smart Barbershop Booking Platform"}

# Barber routes
@api_router.get("/barbers", response_model=List[Barber])
async def get_barbers(city: Optional[str] = None, service_type: Optional[str] = None):
    filter_dict = {"is_available": True}
    if city:
        filter_dict["location.city"] = {"$regex": city, "$options": "i"}
    
    barbers = await db.barbers.find(filter_dict).to_list(100)
    return [Barber(**barber) for barber in barbers]

@api_router.get("/barbers/{barber_id}", response_model=Barber)
async def get_barber(barber_id: str):
    barber = await db.barbers.find_one({"id": barber_id})
    if not barber:
        raise HTTPException(status_code=404, detail="Barber not found")
    return Barber(**barber)

@api_router.post("/barbers", response_model=Barber)
async def create_barber(barber_data: BarberCreate):
    barber_dict = barber_data.dict()
    barber = Barber(**barber_dict)
    await db.barbers.insert_one(barber.dict())
    return barber

# Barber registration for new barbers joining platform
@api_router.post("/barber-registration")
async def register_barber(registration_data: BarberRegistration):
    # Check if barber already exists
    existing_barber = await db.barbers.find_one({"email": registration_data.email})
    if existing_barber:
        raise HTTPException(status_code=400, detail="Barber with this email already exists")
    
    # Create location object
    location = Location(
        address=registration_data.address,
        city=registration_data.city,
        state=registration_data.state,
        postal_code=registration_data.postal_code
    )
    
    # Create new barber profile
    barber_dict = registration_data.dict()
    barber_dict.pop("address")
    barber_dict.pop("city") 
    barber_dict.pop("state")
    barber_dict.pop("postal_code")
    barber_dict.pop("experience_years")
    
    barber_dict["location"] = location.dict()
    barber_dict["profile_image"] = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
    barber_dict["services"] = []
    barber_dict["working_hours"] = {}
    
    barber = Barber(**barber_dict)
    await db.barbers.insert_one(barber.dict())
    
    return {"message": "Registration successful! Your profile is under review.", "barber_id": barber.id}

# Customer routes
@api_router.post("/customers", response_model=Customer)
async def create_customer(customer_data: CustomerCreate):
    existing_customer = await db.customers.find_one({"email": customer_data.email})
    if existing_customer:
        return Customer(**existing_customer)
    
    customer_dict = customer_data.dict()
    customer = Customer(**customer_dict)
    await db.customers.insert_one(customer.dict())
    return customer

# Booking routes with real-time queue updates
@api_router.post("/bookings", response_model=Booking)
async def create_booking(booking_data: BookingCreate):
    barber = await db.barbers.find_one({"id": booking_data.barber_id})
    if not barber:
        raise HTTPException(status_code=404, detail="Barber not found")
    
    service = None
    for s in barber.get("services", []):
        if s["id"] == booking_data.service_id:
            service = s
            break
    
    if not service:
        raise HTTPException(status_code=404, detail="Service not found")
    
    # Calculate queue position and wait time
    current_bookings = await db.bookings.find({
        "barber_id": booking_data.barber_id,
        "status": {"$in": ["pending", "confirmed", "in_progress"]},
        "appointment_date": {"$gte": datetime.utcnow()}
    }).to_list(100)
    
    queue_position = len(current_bookings) + 1
    estimated_wait_time = queue_position * service["duration"]
    
    booking_dict = booking_data.dict()
    booking_dict["total_amount"] = service["price"]
    booking_dict["queue_position"] = queue_position
    booking_dict["estimated_wait_time"] = estimated_wait_time
    
    booking = Booking(**booking_dict)
    await db.bookings.insert_one(booking.dict())
    
    # Update queue and broadcast to connected clients
    await broadcast_queue_update(booking_data.barber_id, queue_position, estimated_wait_time)
    
    return booking

@api_router.get("/bookings", response_model=List[Booking])
async def get_bookings(customer_id: Optional[str] = None, barber_id: Optional[str] = None):
    filter_dict = {}
    if customer_id:
        filter_dict["customer_id"] = customer_id
    if barber_id:
        filter_dict["barber_id"] = barber_id
    
    bookings = await db.bookings.find(filter_dict).to_list(100)
    return [Booking(**booking) for booking in bookings]

# Queue management
@api_router.get("/queue/{barber_id}")
async def get_queue_status(barber_id: str):
    current_bookings = await db.bookings.find({
        "barber_id": barber_id,
        "status": {"$in": ["pending", "confirmed", "in_progress"]},
        "appointment_date": {"$gte": datetime.utcnow()}
    }).sort("appointment_date").to_list(50)
    
    queue_info = {
        "barber_id": barber_id,
        "queue_size": len(current_bookings),
        "estimated_wait_time": sum([b["estimated_wait_time"] for b in current_bookings]),
        "next_available": datetime.utcnow() + timedelta(minutes=sum([b["estimated_wait_time"] for b in current_bookings[:3]])) if current_bookings else datetime.utcnow()
    }
    
    return queue_info

# Payment routes (updated for INR)
@api_router.post("/payments/checkout/session")
async def create_checkout_session(request: Request, booking_id: str, origin_url: str):
    if not stripe_api_key:
        raise HTTPException(status_code=500, detail="Stripe not configured")
    
    booking = await db.bookings.find_one({"id": booking_id})
    if not booking:
        raise HTTPException(status_code=404, detail="Booking not found")
    
    customer = await db.customers.find_one({"id": booking["customer_id"]})
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    
    try:
        host_url = origin_url
        webhook_url = f"{host_url}/api/webhook/stripe"
        stripe_checkout = StripeCheckout(api_key=stripe_api_key, webhook_url=webhook_url)
        
        success_url = f"{host_url}/booking-success?session_id={{CHECKOUT_SESSION_ID}}"
        cancel_url = f"{host_url}/booking-cancelled"
        
        checkout_request = CheckoutSessionRequest(
            amount=booking["total_amount"],
            currency="inr",  # Changed to INR
            success_url=success_url,
            cancel_url=cancel_url,
            metadata={
                "booking_id": booking_id,
                "customer_id": booking["customer_id"],
                "customer_email": customer["email"]
            }
        )
        
        session = await stripe_checkout.create_checkout_session(checkout_request)
        
        transaction = PaymentTransaction(
            session_id=session.session_id,
            booking_id=booking_id,
            customer_id=booking["customer_id"],
            amount=booking["total_amount"],
            currency="inr",
            payment_status="initiated",
            status="initiated",
            metadata=checkout_request.metadata
        )
        
        await db.payment_transactions.insert_one(transaction.dict())
        await db.bookings.update_one(
            {"id": booking_id},
            {"$set": {"payment_session_id": session.session_id}}
        )
        
        return {"url": session.url, "session_id": session.session_id}
        
    except Exception as e:
        logger.error(f"Error creating checkout session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.get("/payments/checkout/status/{session_id}")
async def get_checkout_status(session_id: str):
    if not stripe_api_key:
        raise HTTPException(status_code=500, detail="Stripe not configured")
    
    try:
        stripe_checkout = StripeCheckout(api_key=stripe_api_key, webhook_url="")
        status_response = await stripe_checkout.get_checkout_status(session_id)
        
        transaction = await db.payment_transactions.find_one({"session_id": session_id})
        if transaction:
            update_data = {
                "payment_status": status_response.payment_status,
                "status": status_response.status,
                "updated_at": datetime.utcnow()
            }
            
            await db.payment_transactions.update_one(
                {"session_id": session_id},
                {"$set": update_data}
            )
            
            if status_response.payment_status == "paid":
                booking_id = transaction["booking_id"]
                existing_booking = await db.bookings.find_one({"id": booking_id, "payment_status": {"$ne": "paid"}})
                
                if existing_booking:
                    await db.bookings.update_one(
                        {"id": booking_id},
                        {"$set": {"payment_status": "paid", "status": "confirmed"}}
                    )
                    
                    # Broadcast queue update
                    await broadcast_queue_update(
                        existing_booking["barber_id"],
                        existing_booking.get("queue_position", 0),
                        existing_booking.get("estimated_wait_time", 0)
                    )
        
        return {
            "status": status_response.status,
            "payment_status": status_response.payment_status,
            "amount_total": status_response.amount_total,
            "currency": status_response.currency,
            "metadata": status_response.metadata
        }
        
    except Exception as e:
        logger.error(f"Error checking payment status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.post("/webhook/stripe")
async def stripe_webhook(request: Request):
    if not stripe_api_key:
        raise HTTPException(status_code=500, detail="Stripe not configured")
    
    try:
        body = await request.body()
        stripe_signature = request.headers.get("stripe-signature")
        
        if not stripe_signature:
            raise HTTPException(status_code=400, detail="Missing Stripe signature")
        
        stripe_checkout = StripeCheckout(api_key=stripe_api_key, webhook_url="")
        webhook_response = await stripe_checkout.handle_webhook(body, stripe_signature)
        
        if webhook_response.session_id:
            transaction = await db.payment_transactions.find_one({"session_id": webhook_response.session_id})
            if transaction:
                await db.payment_transactions.update_one(
                    {"session_id": webhook_response.session_id},
                    {"$set": {
                        "payment_status": webhook_response.payment_status,
                        "updated_at": datetime.utcnow()
                    }}
                )
                
                if webhook_response.payment_status == "paid":
                    booking_id = transaction["booking_id"]
                    existing_booking = await db.bookings.find_one({"id": booking_id, "payment_status": {"$ne": "paid"}})
                    
                    if existing_booking:
                        await db.bookings.update_one(
                            {"id": booking_id},
                            {"$set": {"payment_status": "paid", "status": "confirmed"}}
                        )
        
        return {"status": "success"}
        
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Reviews routes with barber response capability
@api_router.post("/reviews", response_model=Review)
async def create_review(review_data: Review):
    await db.reviews.insert_one(review_data.dict())
    
    reviews = await db.reviews.find({"barber_id": review_data.barber_id}).to_list(1000)
    if reviews:
        avg_rating = sum(r["rating"] for r in reviews) / len(reviews)
        await db.barbers.update_one(
            {"id": review_data.barber_id},
            {"$set": {"rating": round(avg_rating, 1), "total_reviews": len(reviews)}}
        )
    
    return review_data

@api_router.get("/reviews/barber/{barber_id}", response_model=List[Review])
async def get_barber_reviews(barber_id: str, limit: int = 50):
    reviews = await db.reviews.find({"barber_id": barber_id}).sort("created_at", -1).limit(limit).to_list(limit)
    return [Review(**review) for review in reviews]

@api_router.put("/reviews/{review_id}/response")
async def respond_to_review(review_id: str, response_data: Dict[str, str]):
    result = await db.reviews.update_one(
        {"id": review_id},
        {"$set": {"response": response_data.get("response", "")}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Review not found")
    
    return {"message": "Response added successfully"}

# Services information endpoint
@api_router.get("/services")
async def get_services_info():
    return {
        "popular_services": [
            {
                "name": "Basic Haircut",
                "description": "Professional haircut with basic styling",
                "price_range": "₹200 - ₹400",
                "duration": "30-45 minutes",
                "image": "https://images.unsplash.com/photo-1593702275687-f8b402bf1fb5?w=400&h=300"
            },
            {
                "name": "Premium Cut & Style", 
                "description": "Complete haircut with advanced styling and finish",
                "price_range": "₹400 - ₹700",
                "duration": "45-60 minutes",
                "image": "https://images.unsplash.com/photo-1605497788044-5a32c7078486?w=400&h=300"
            },
            {
                "name": "Beard Trim & Shape",
                "description": "Professional beard trimming and shaping",
                "price_range": "₹150 - ₹300", 
                "duration": "20-30 minutes",
                "image": "https://images.unsplash.com/photo-1514336937476-a5b961020a5c?w=400&h=300"
            },
            {
                "name": "Hot Towel Shave",
                "description": "Traditional hot towel shave experience",
                "price_range": "₹300 - ₹500",
                "duration": "30-45 minutes",
                "image": "https://images.unsplash.com/photo-1647140655214-e4a2d914971f?w=400&h=300"
            }
        ],
        "specialty_services": [
            {
                "name": "Wedding Package",
                "description": "Complete grooming for your special day",
                "price_range": "₹1200 - ₹2500",
                "duration": "90-120 minutes"
            },
            {
                "name": "Hair Coloring", 
                "description": "Professional hair coloring and highlights",
                "price_range": "₹800 - ₹2000",
                "duration": "90-150 minutes"
            }
        ]
    }

# Contact information endpoint
@api_router.get("/contact")
async def get_contact_info():
    return {
        "company": {
            "name": "TrimTime Technologies",
            "tagline": "Smart Barbershop Booking Platform",
            "founded": "2024"
        },
        "contact": {
            "email": "<EMAIL>",
            "phone": "+91-11-4567-8900",
            "whatsapp": "+91-98765-43210"
        },
        "offices": [
            {
                "city": "New Delhi",
                "address": "Block A, Sector 16, Noida, Uttar Pradesh 201301",
                "phone": "+91-11-4567-8900"
            },
            {
                "city": "Mumbai", 
                "address": "Bandra Kurla Complex, Mumbai, Maharashtra 400051",
                "phone": "+91-22-6789-0123"
            },
            {
                "city": "Bangalore",
                "address": "Koramangala, Bangalore, Karnataka 560095", 
                "phone": "+91-80-2345-6789"
            }
        ],
        "support_hours": {
            "weekdays": "9:00 AM - 9:00 PM",
            "weekends": "10:00 AM - 6:00 PM"
        },
        "social_media": {
            "facebook": "https://facebook.com/trimtime.in",
            "instagram": "https://instagram.com/trimtime_official",
            "twitter": "https://twitter.com/trimtime_in"
        }
    }

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=os.environ.get('CORS_ORIGINS', '*').split(','),
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_db():
    await create_demo_barbers()

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()

# Mount Socket.IO
app = socket_app