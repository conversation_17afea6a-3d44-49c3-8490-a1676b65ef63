import React, { useEffect, useState } from "react";
import "./App.css";
import { BrowserRouter, Routes, Route, useNavigate, useLocation } from "react-router-dom";
import axios from "axios";
import { io } from 'socket.io-client';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
const API = `${BACKEND_URL}/api`;

// Socket.IO connection
let socket;

// Loading Spinner Component
const LoadingSpinner = () => (
  <div className="flex justify-center items-center p-8">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
  </div>
);

// Star Rating Component
const StarRating = ({ rating, size = "w-4 h-4" }) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  for (let i = 0; i < fullStars; i++) {
    stars.push(
      <svg key={i} className={`${size} text-yellow-400 fill-current`} viewBox="0 0 20 20">
        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
      </svg>
    );
  }

  if (hasHalfStar) {
    stars.push(
      <svg key="half" className={`${size} text-yellow-400`} viewBox="0 0 20 20">
        <defs>
          <linearGradient id="half">
            <stop offset="50%" stopColor="#FCD34D" />
            <stop offset="50%" stopColor="#D1D5DB" />
          </linearGradient>
        </defs>
        <path fill="url(#half)" d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
      </svg>
    );
  }

  const remainingStars = 5 - stars.length;
  for (let i = 0; i < remainingStars; i++) {
    stars.push(
      <svg key={`empty-${i}`} className={`${size} text-gray-300`} viewBox="0 0 20 20">
        <path fill="currentColor" d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
      </svg>
    );
  }

  return <div className="flex items-center">{stars}</div>;
};

// Real-time Queue Component
const QueueStatus = ({ barberId, isVisible }) => {
  const [queueInfo, setQueueInfo] = useState({ size: 0, wait_time: 0 });

  useEffect(() => {
    if (!isVisible || !barberId) return;

    // Initialize socket connection
    if (!socket) {
      socket = io(BACKEND_URL);
    }

    // Join barber's queue updates
    socket.emit('join_barber_queue', { barber_id: barberId });

    // Listen for queue updates
    socket.on('queue_update', (data) => {
      if (data.barber_id === barberId || !data.barber_id) {
        setQueueInfo({
          size: data.queue_size || data.size || 0,
          wait_time: data.estimated_wait_time || data.wait_time || 0
        });
      }
    });

    return () => {
      if (socket) {
        socket.emit('leave_barber_queue', { barber_id: barberId });
      }
    };
  }, [barberId, isVisible]);

  if (!isVisible) return null;

  return (
    <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-lg shadow-lg">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium">Current Queue</p>
          <p className="text-2xl font-bold">{queueInfo.size} people</p>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium">Est. Wait Time</p>
          <p className="text-2xl font-bold">{queueInfo.wait_time} min</p>
        </div>
      </div>
    </div>
  );
};

// Header Component with enhanced navigation
const Header = ({ currentPage, setCurrentPage }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigationItems = [
    { id: 'home', label: 'Home', icon: '🏠' },
    { id: 'barbers', label: 'Barbers', icon: '✂️' },
    { id: 'services', label: 'Services', icon: '💼' },
    { id: 'contact', label: 'Contact', icon: '📞' },
    { id: 'join-barber', label: 'Join as Barber', icon: '👨‍💼' }
  ];

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center cursor-pointer" onClick={() => setCurrentPage('home')}>
            <div className="flex-shrink-0">
              <h1 className="text-2xl font-bold text-gray-900">
                <span className="text-blue-600">Trim</span>Time
              </h1>
              <p className="text-xs text-gray-500">Smart Barbershop Booking</p>
            </div>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentPage(item.id)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition duration-300 ${
                  currentPage === item.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-900 hover:text-blue-600 hover:bg-blue-50'
                }`}
              >
                <span>{item.icon}</span>
                <span>{item.label}</span>
              </button>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <div className="w-6 h-6 flex flex-col justify-center">
              <span className={`bg-gray-600 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMenuOpen ? 'rotate-45 translate-y-1' : '-translate-y-0.5'}`}></span>
              <span className={`bg-gray-600 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm my-0.5 ${isMenuOpen ? 'opacity-0' : 'opacity-100'}`}></span>
              <span className={`bg-gray-600 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMenuOpen ? '-rotate-45 -translate-y-1' : 'translate-y-0.5'}`}></span>
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200">
            <div className="py-2">
              {navigationItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    setCurrentPage(item.id);
                    setIsMenuOpen(false);
                  }}
                  className={`flex items-center space-x-3 w-full px-4 py-3 text-left transition duration-300 ${
                    currentPage === item.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-900 hover:bg-blue-50'
                  }`}
                >
                  <span>{item.icon}</span>
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

// Enhanced Hero Section
const HeroSection = ({ onBookNow }) => {
  return (
    <section className="relative min-h-screen flex items-center justify-center">
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1503951914875-452162b0f3f1?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NDk1NzZ8MHwxfHNlYXJjaHwxfHxiYXJiZXJzaG9wfGVufDB8fHx8MTc1NDgyMDcxM3ww&ixlib=rb-4.1.0&q=85')`
        }}
      >
        {/* Animated overlay pattern */}
        <div className="absolute inset-0 bg-pattern opacity-10"></div>
      </div>
      
      <div className="relative z-10 text-center text-white max-w-6xl mx-auto px-4">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fadeIn">
          Smart Barbershop 
          <span className="text-blue-400 block"> Booking</span>
        </h1>
        <p className="text-xl md:text-2xl mb-8 text-gray-200 animate-fadeIn">
          Skip the wait. Book your perfect cut with top-rated barbers across India.
        </p>
        
        {/* Features highlight */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10 animate-fadeIn">
          <div className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg p-6 rounded-xl">
            <div className="text-4xl mb-3">⚡</div>
            <h3 className="font-semibold text-lg mb-2">Instant Booking</h3>
            <p className="text-sm text-gray-300">Book in seconds, get confirmed instantly</p>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg p-6 rounded-xl">
            <div className="text-4xl mb-3">📍</div>
            <h3 className="font-semibold text-lg mb-2">Real-time Queue</h3>
            <p className="text-sm text-gray-300">Live updates on wait times and queue status</p>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg p-6 rounded-xl">
            <div className="text-4xl mb-3">💳</div>
            <h3 className="font-semibold text-lg mb-2">Secure Payments</h3>
            <p className="text-sm text-gray-300">Safe online payments in Indian Rupees</p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button 
            onClick={onBookNow}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition duration-300 shadow-xl"
          >
            🚀 Book Appointment
          </button>
          <button 
            onClick={onBookNow}
            className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition duration-300 backdrop-filter backdrop-blur-sm"
          >
            👀 View Barbers
          </button>
        </div>

        {/* Stats */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400">1000+</div>
            <div className="text-sm text-gray-300">Happy Customers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400">50+</div>
            <div className="text-sm text-gray-300">Expert Barbers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400">15+</div>
            <div className="text-sm text-gray-300">Cities</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400">4.8★</div>
            <div className="text-sm text-gray-300">Average Rating</div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Enhanced Barber Card Component
const BarberCard = ({ barber, onBookAppointment }) => {
  const [showQueue, setShowQueue] = useState(false);

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition duration-300 card-hover">
      <div className="relative h-64">
        <img
          src={barber.profile_image}
          alt={barber.name}
          className="w-full h-full object-cover image-zoom"
        />
        <div className="absolute top-4 right-4">
          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
            barber.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {barber.is_available ? '🟢 Available' : '🔴 Busy'}
          </span>
        </div>
        <div className="absolute top-4 left-4 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs font-semibold">
          📍 {barber.location?.city || 'Location TBD'}
        </div>
      </div>
      
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div>
            <h3 className="text-xl font-bold text-gray-900">{barber.name}</h3>
            {barber.shop_name && (
              <p className="text-sm text-gray-600">{barber.shop_name}</p>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <StarRating rating={barber.rating} />
            <span className="text-sm text-gray-600 ml-1">({barber.total_reviews})</span>
          </div>
        </div>
        
        <p className="text-gray-600 mb-4 text-sm">{barber.bio}</p>
        
        {/* Languages */}
        {barber.languages && barber.languages.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-1">Languages:</p>
            <div className="flex flex-wrap gap-1">
              {barber.languages.map((lang, index) => (
                <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                  {lang}
                </span>
              ))}
            </div>
          </div>
        )}
        
        <div className="mb-4">
          <h4 className="font-semibold text-gray-900 mb-2">Specialties:</h4>
          <div className="flex flex-wrap gap-2">
            {barber.specialties.slice(0, 3).map((specialty, index) => (
              <span key={index} className="specialty-tag">
                {specialty}
              </span>
            ))}
          </div>
        </div>
        
        <div className="mb-4">
          <h4 className="font-semibold text-gray-900 mb-2">Services:</h4>
          <div className="space-y-2">
            {barber.services.slice(0, 3).map((service) => (
              <div key={service.id} className="flex justify-between text-sm">
                <span>{service.name}</span>
                <span className="font-semibold service-price">₹{service.price}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Real-time Queue Status */}
        <div className="mb-4">
          <button
            onClick={() => setShowQueue(!showQueue)}
            className="w-full text-left p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200"
          >
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">📊 Queue Status</span>
              <span className="text-xs text-gray-500">{showQueue ? '▼' : '▶'}</span>
            </div>
          </button>
          <QueueStatus barberId={barber.id} isVisible={showQueue} />
        </div>
        
        <button
          onClick={() => onBookAppointment(barber)}
          className="w-full btn-primary"
        >
          📅 Book Appointment
        </button>
      </div>
    </div>
  );
};

// Enhanced Booking Form Component
const BookingForm = ({ barber, onClose, onBookingComplete }) => {
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    selectedService: '',
    appointmentDate: '',
    appointmentTime: '',
    notes: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Create customer
      const customerResponse = await axios.post(`${API}/customers`, {
        name: formData.customerName,
        email: formData.customerEmail,
        phone: formData.customerPhone
      });

      const customer = customerResponse.data;

      // Create booking
      const appointmentDateTime = new Date(`${formData.appointmentDate}T${formData.appointmentTime}`);
      const bookingResponse = await axios.post(`${API}/bookings`, {
        customer_id: customer.id,
        barber_id: barber.id,
        service_id: formData.selectedService,
        appointment_date: appointmentDateTime.toISOString(),
        notes: formData.notes
      });

      const booking = bookingResponse.data;

      // Create payment session
      const paymentResponse = await axios.post(`${API}/payments/checkout/session`, null, {
        params: {
          booking_id: booking.id,
          origin_url: window.location.origin
        }
      });

      // Redirect to Stripe checkout
      if (paymentResponse.data.url) {
        window.location.href = paymentResponse.data.url;
      }
      
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to create booking');
    } finally {
      setLoading(false);
    }
  };

  // Generate time slots
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 9; hour <= 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        slots.push(time);
      }
    }
    return slots;
  };

  const selectedService = barber.services.find(s => s.id === formData.selectedService);

  return (
    <div className="booking-form-overlay">
      <div className="booking-form">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Book with {barber.name}</h2>
              <p className="text-gray-600">{barber.shop_name || 'Professional Barbershop'}</p>
            </div>
            <button 
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-3xl font-light"
            >
              ×
            </button>
          </div>

          {error && (
            <div className="message-error mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">👤 Full Name *</label>
                <input
                  type="text"
                  name="customerName"
                  value={formData.customerName}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                  placeholder="Enter your full name"
                />
              </div>
              <div>
                <label className="form-label">📧 Email Address *</label>
                <input
                  type="email"
                  name="customerEmail"
                  value={formData.customerEmail}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <label className="form-label">📱 Phone Number *</label>
              <input
                type="tel"
                name="customerPhone"
                value={formData.customerPhone}
                onChange={handleInputChange}
                required
                className="form-input"
                placeholder="+91 98765 43210"
              />
            </div>

            <div>
              <label className="form-label">✂️ Select Service *</label>
              <select
                name="selectedService"
                value={formData.selectedService}
                onChange={handleInputChange}
                required
                className="form-input"
              >
                <option value="">Choose a service...</option>
                {barber.services.map((service) => (
                  <option key={service.id} value={service.id}>
                    {service.name} - ₹{service.price} ({service.duration} min)
                  </option>
                ))}
              </select>
              {selectedService && (
                <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>{selectedService.name}</strong> - ₹{selectedService.price}
                  </p>
                  <p className="text-xs text-blue-600">{selectedService.description}</p>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">📅 Date *</label>
                <input
                  type="date"
                  name="appointmentDate"
                  value={formData.appointmentDate}
                  onChange={handleInputChange}
                  required
                  min={new Date().toISOString().split('T')[0]}
                  className="form-input"
                />
              </div>
              <div>
                <label className="form-label">🕐 Time *</label>
                <select
                  name="appointmentTime"
                  value={formData.appointmentTime}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                >
                  <option value="">Choose time...</option>
                  {generateTimeSlots().map((time) => (
                    <option key={time} value={time}>{time}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="form-label">📝 Special Requests</label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows="3"
                className="form-input"
                placeholder="Any special requests or preferences..."
              />
            </div>

            {selectedService && (
              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">💳 Payment Summary</h4>
                <div className="flex justify-between items-center">
                  <span>{selectedService.name}</span>
                  <span className="text-2xl font-bold text-green-600">₹{selectedService.price}</span>
                </div>
                <p className="text-xs text-gray-600 mt-1">Secure payment processing via Stripe</p>
              </div>
            )}

            <div className="flex gap-4 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline flex-1"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="btn-primary flex-1"
              >
                {loading ? '⏳ Processing...' : '💳 Proceed to Payment'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Services Page Component
const ServicesPage = () => {
  const [servicesData, setServicesData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      const response = await axios.get(`${API}/services`);
      setServicesData(response.data);
    } catch (error) {
      console.error('Error fetching services:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <LoadingSpinner />;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Our Services</h1>
          <p className="text-xl text-gray-600">Professional grooming services at competitive prices</p>
        </div>

        {/* Popular Services */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Popular Services</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {servicesData?.popular_services?.map((service, index) => (
              <div key={index} className="service-card">
                <div className="h-48 mb-4 rounded-lg overflow-hidden">
                  <img 
                    src={service.image}
                    alt={service.name}
                    className="w-full h-full object-cover hover:scale-110 transition duration-300"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{service.name}</h3>
                <p className="text-gray-600 mb-3">{service.description}</p>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-lg font-semibold text-green-600">{service.price_range}</span>
                  <span className="text-sm text-gray-500">⏱ {service.duration}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Specialty Services */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Specialty Services</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {servicesData?.specialty_services?.map((service, index) => (
              <div key={index} className="bg-gradient-to-r from-purple-500 to-blue-600 text-white p-8 rounded-xl">
                <h3 className="text-2xl font-bold mb-3">{service.name}</h3>
                <p className="text-purple-100 mb-4">{service.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-bold">{service.price_range}</span>
                  <span className="text-purple-200">⏱ {service.duration}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Why Choose Our Services */}
        <div className="bg-white p-8 rounded-xl shadow-lg">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Why Choose TrimTime Services?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🏆</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Expert Professionals</h3>
              <p className="text-gray-600">All our barbers are skilled professionals with years of experience</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">💰</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Competitive Pricing</h3>
              <p className="text-gray-600">Quality services at affordable prices across all locations</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">✨</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Premium Quality</h3>
              <p className="text-gray-600">Using only the finest products and latest techniques</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Contact Page Component
const ContactPage = () => {
  const [contactData, setContactData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  useEffect(() => {
    fetchContactInfo();
  }, []);

  const fetchContactInfo = async () => {
    try {
      const response = await axios.get(`${API}/contact`);
      setContactData(response.data);
    } catch (error) {
      console.error('Error fetching contact info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    alert('Thank you for your message! We will get back to you soon.');
    setFormData({ name: '', email: '', message: '' });
  };

  if (loading) return <LoadingSpinner />;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Contact Us</h1>
          <p className="text-xl text-gray-600">Get in touch with TrimTime - We're here to help!</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div>
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">📞 Get In Touch</h2>
              
              <div className="space-y-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-2xl">📧</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Email</h3>
                    <p className="text-gray-600">{contactData?.contact?.email}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-2xl">📞</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Phone</h3>
                    <p className="text-gray-600">{contactData?.contact?.phone}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-2xl">💬</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">WhatsApp</h3>
                    <p className="text-gray-600">{contactData?.contact?.whatsapp}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Office Locations */}
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">🏢 Our Offices</h2>
              <div className="space-y-6">
                {contactData?.offices?.map((office, index) => (
                  <div key={index} className="border-l-4 border-blue-500 pl-4">
                    <h3 className="font-bold text-lg text-gray-900">{office.city}</h3>
                    <p className="text-gray-600 mb-1">{office.address}</p>
                    <p className="text-sm text-blue-600">{office.phone}</p>
                  </div>
                ))}
              </div>

              <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">📅 Support Hours</h4>
                <p className="text-sm text-gray-600">Weekdays: {contactData?.support_hours?.weekdays}</p>
                <p className="text-sm text-gray-600">Weekends: {contactData?.support_hours?.weekends}</p>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">📨 Send us a Message</h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="form-label">👤 Your Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                  className="form-input"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="form-label">📧 Email Address</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  required
                  className="form-input"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="form-label">💬 Message</label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData({...formData, message: e.target.value})}
                  required
                  rows="6"
                  className="form-input"
                  placeholder="Tell us how we can help you..."
                />
              </div>

              <button
                type="submit"
                className="btn-primary w-full"
              >
                🚀 Send Message
              </button>
            </form>

            {/* Social Media */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h4 className="font-semibold text-gray-900 mb-4">🌐 Follow Us</h4>
              <div className="flex space-x-4">
                <a href={contactData?.social_media?.facebook} className="text-blue-600 hover:text-blue-800">
                  📘 Facebook
                </a>
                <a href={contactData?.social_media?.instagram} className="text-pink-600 hover:text-pink-800">
                  📸 Instagram
                </a>
                <a href={contactData?.social_media?.twitter} className="text-blue-400 hover:text-blue-600">
                  🐦 Twitter
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Barber Registration Page Component
const BarberRegistrationPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    shop_name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    bio: '',
    experience_years: '',
    specialties: [],
    languages: [],
    certifications: []
  });
  
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const specialtyOptions = [
    'Modern Fades', 'Classic Cuts', 'Beard Styling', 'Wedding Prep',
    'Contemporary Styles', 'Precision Cuts', 'Hair Coloring', 'Texture Styling',
    'Traditional Cuts', 'Hot Towel Shaves', 'Luxury Grooming'
  ];

  const languageOptions = ['Hindi', 'English', 'Punjabi', 'Marathi', 'Urdu', 'Tamil', 'Telugu', 'Bengali'];

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleMultiSelectChange = (field, value) => {
    const currentValues = formData[field];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(item => item !== value)
      : [...currentValues, value];
    
    setFormData({
      ...formData,
      [field]: newValues
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post(`${API}/barber-registration`, {
        ...formData,
        experience_years: parseInt(formData.experience_years)
      });
      
      setSuccess(true);
    } catch (err) {
      setError(err.response?.data?.detail || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-3xl">🎉</span>
          </div>
          <h2 className="text-2xl font-bold text-green-900 mb-4">Registration Successful!</h2>
          <p className="text-gray-600 mb-6">
            Thank you for joining TrimTime! Your profile is under review and we'll contact you within 2-3 business days.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary w-full"
          >
            Register Another Barber
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Join TrimTime as a Barber</h1>
          <p className="text-xl text-gray-600">Grow your business with India's leading barbershop booking platform</p>
        </div>

        {/* Benefits Section */}
        <div className="bg-white p-8 rounded-xl shadow-lg mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why Join TrimTime?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">📈</span>
              </div>
              <h3 className="font-semibold text-lg mb-2">Increase Bookings</h3>
              <p className="text-gray-600 text-sm">Reach more customers and fill your schedule</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">💳</span>
              </div>
              <h3 className="font-semibold text-lg mb-2">Secure Payments</h3>
              <p className="text-gray-600 text-sm">Get paid instantly for every service</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">📊</span>
              </div>
              <h3 className="font-semibold text-lg mb-2">Business Insights</h3>
              <p className="text-gray-600 text-sm">Track your performance and grow</p>
            </div>
          </div>
        </div>

        {/* Registration Form */}
        <div className="bg-white p-8 rounded-xl shadow-lg">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">👨‍💼 Registration Form</h2>
          
          {error && (
            <div className="message-error mb-6">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">📝 Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="form-label">👤 Full Name *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label className="form-label">🏪 Shop Name (Optional)</label>
                  <input
                    type="text"
                    name="shop_name"
                    value={formData.shop_name}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="Your barbershop name"
                  />
                </div>
                <div>
                  <label className="form-label">📧 Email Address *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="form-label">📱 Phone Number *</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="+91 98765 43210"
                  />
                </div>
              </div>
            </div>

            {/* Location */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">📍 Location Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="form-label">🏢 Full Address *</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="Shop address with landmark"
                  />
                </div>
                <div>
                  <label className="form-label">🏙️ City *</label>
                  <input
                    type="text"
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="City name"
                  />
                </div>
                <div>
                  <label className="form-label">📍 State *</label>
                  <input
                    type="text"
                    name="state"
                    value={formData.state}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="State name"
                  />
                </div>
                <div>
                  <label className="form-label">📮 Postal Code *</label>
                  <input
                    type="text"
                    name="postal_code"
                    value={formData.postal_code}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="PIN Code"
                  />
                </div>
              </div>
            </div>

            {/* Professional Details */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">💼 Professional Details</h3>
              <div className="space-y-4">
                <div>
                  <label className="form-label">👨‍🎓 Experience (Years) *</label>
                  <select
                    name="experience_years"
                    value={formData.experience_years}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                  >
                    <option value="">Select experience...</option>
                    {[...Array(20)].map((_, i) => (
                      <option key={i} value={i + 1}>{i + 1} year{i > 0 ? 's' : ''}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="form-label">📝 About Yourself *</label>
                  <textarea
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    required
                    rows="4"
                    className="form-input"
                    placeholder="Tell us about your experience and what makes you special..."
                  />
                </div>
              </div>
            </div>

            {/* Skills & Languages */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">🎯 Skills & Languages</h3>
              
              <div className="mb-6">
                <label className="form-label">✂️ Specialties *</label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {specialtyOptions.map((specialty) => (
                    <label key={specialty} className="flex items-center p-2 border rounded cursor-pointer hover:bg-blue-50">
                      <input
                        type="checkbox"
                        checked={formData.specialties.includes(specialty)}
                        onChange={() => handleMultiSelectChange('specialties', specialty)}
                        className="mr-2"
                      />
                      <span className="text-sm">{specialty}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="form-label">🗣️ Languages Spoken</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {languageOptions.map((language) => (
                    <label key={language} className="flex items-center p-2 border rounded cursor-pointer hover:bg-green-50">
                      <input
                        type="checkbox"
                        checked={formData.languages.includes(language)}
                        onChange={() => handleMultiSelectChange('languages', language)}
                        className="mr-2"
                      />
                      <span className="text-sm">{language}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="pt-6 border-t border-gray-200">
              <button
                type="submit"
                disabled={loading}
                className="btn-primary w-full text-lg"
              >
                {loading ? '⏳ Submitting Registration...' : '🚀 Join TrimTime Platform'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Success Page Component (Enhanced)
const BookingSuccessPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [paymentStatus, setPaymentStatus] = useState('checking');
  const [sessionId, setSessionId] = useState('');

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const sessionIdFromUrl = urlParams.get('session_id');
    
    if (sessionIdFromUrl) {
      setSessionId(sessionIdFromUrl);
      pollPaymentStatus(sessionIdFromUrl);
    } else {
      setPaymentStatus('error');
    }
  }, [location]);

  const pollPaymentStatus = async (sessionId, attempts = 0) => {
    const maxAttempts = 5;
    
    if (attempts >= maxAttempts) {
      setPaymentStatus('timeout');
      return;
    }

    try {
      const response = await axios.get(`${API}/payments/checkout/status/${sessionId}`);
      const data = response.data;
      
      if (data.payment_status === 'paid') {
        setPaymentStatus('success');
        return;
      } else if (data.status === 'expired') {
        setPaymentStatus('expired');
        return;
      }

      setTimeout(() => pollPaymentStatus(sessionId, attempts + 1), 2000);
    } catch (error) {
      console.error('Error checking payment status:', error);
      setPaymentStatus('error');
    }
  };

  const StatusDisplay = () => {
    switch (paymentStatus) {
      case 'checking':
        return (
          <div className="text-center">
            <LoadingSpinner />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Verifying Payment...</h2>
            <p className="text-gray-600">Please wait while we confirm your payment with the bank.</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
              <span className="text-4xl">🎉</span>
            </div>
            <h2 className="text-3xl font-bold text-green-900 mb-4">Booking Confirmed!</h2>
            <p className="text-gray-600 mb-6">
              Your appointment has been successfully booked and payment has been processed in ₹ (INR). 
              You'll receive a confirmation email and SMS shortly.
            </p>
            <div className="bg-green-50 p-4 rounded-lg mb-6">
              <p className="text-sm text-green-800">
                📧 Check your email for booking details and barber contact information.
              </p>
            </div>
            <button
              onClick={() => navigate('/')}
              className="btn-primary"
            >
              🏠 Back to Home
            </button>
          </div>
        );
      
      default:
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
              <span className="text-4xl">❌</span>
            </div>
            <h2 className="text-3xl font-bold text-red-900 mb-4">Payment Error</h2>
            <p className="text-gray-600 mb-6">
              There was an error processing your payment. Please try again or contact our support team.
            </p>
            <button
              onClick={() => navigate('/')}
              className="btn-primary"
            >
              🔄 Try Again
            </button>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <StatusDisplay />
      </div>
    </div>
  );
};

// Cancelled Page Component
const BookingCancelledPage = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-6">
          <span className="text-4xl">⚠️</span>
        </div>
        <h2 className="text-3xl font-bold text-yellow-900 mb-4">Booking Cancelled</h2>
        <p className="text-gray-600 mb-6">
          Your booking was cancelled. No payment was processed. You can try booking again anytime.
        </p>
        <button
          onClick={() => navigate('/')}
          className="btn-primary"
        >
          🔄 Book Again
        </button>
      </div>
    </div>
  );
};

// Enhanced Home Page Component
const HomePage = () => {
  const [barbers, setBarbers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBarber, setSelectedBarber] = useState(null);
  const [showBookingForm, setShowBookingForm] = useState(false);
  
  useEffect(() => {
    fetchBarbers();
    
    // Initialize socket connection for real-time updates
    if (!socket) {
      socket = io(BACKEND_URL);
      socket.on('connect', () => {
        console.log('Connected to TrimTime real-time updates');
      });
    }

    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, []);

  const fetchBarbers = async () => {
    try {
      const response = await axios.get(`${API}/barbers`);
      setBarbers(response.data);
    } catch (error) {
      console.error('Error fetching barbers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = () => {
    document.getElementById('barbers').scrollIntoView({ behavior: 'smooth' });
  };

  const handleBookAppointment = (barber) => {
    setSelectedBarber(barber);
    setShowBookingForm(true);
  };

  const handleCloseBookingForm = () => {
    setShowBookingForm(false);
    setSelectedBarber(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <HeroSection onBookNow={handleBookNow} />
      
      {/* Barbers Section */}
      <section id="barbers" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Expert Barbers</h2>
            <p className="text-xl text-gray-600">Choose from our team of skilled professionals across India</p>
          </div>

          {loading ? (
            <LoadingSpinner />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {barbers.map((barber) => (
                <BarberCard
                  key={barber.id}
                  barber={barber}
                  onBookAppointment={handleBookAppointment}
                />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose TrimTime?</h2>
            <p className="text-xl text-gray-600">India's most trusted barbershop booking platform</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center bg-white p-8 rounded-xl shadow-lg">
              <div className="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">⚡</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Skip the Wait</h3>
              <p className="text-gray-600">Book your appointment online and see real-time queue updates. No more waiting in long lines!</p>
            </div>
            <div className="text-center bg-white p-8 rounded-xl shadow-lg">
              <div className="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">🏆</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Top-Rated Barbers</h3>
              <p className="text-gray-600">All our barbers are verified professionals with excellent ratings and years of experience.</p>
            </div>
            <div className="text-center bg-white p-8 rounded-xl shadow-lg">
              <div className="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">💳</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Secure Payments</h3>
              <p className="text-gray-600">Safe and secure online payments in Indian Rupees with instant confirmation and receipts.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && selectedBarber && (
        <BookingForm
          barber={selectedBarber}
          onClose={handleCloseBookingForm}
          onBookingComplete={() => {
            setShowBookingForm(false);
            setSelectedBarber(null);
          }}
        />
      )}
    </div>
  );
};

// Main App Component with Navigation
function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage />;
      case 'services':
        return <ServicesPage />;
      case 'contact':
        return <ContactPage />;
      case 'join-barber':
        return <BarberRegistrationPage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div className="App">
      <BrowserRouter>
        <Routes>
          <Route path="/booking-success" element={<BookingSuccessPage />} />
          <Route path="/booking-cancelled" element={<BookingCancelledPage />} />
          <Route path="/*" element={
            <div>
              <Header currentPage={currentPage} setCurrentPage={setCurrentPage} />
              {renderPage()}
            </div>
          } />
        </Routes>
      </BrowserRouter>
    </div>
  );
}

export default App;