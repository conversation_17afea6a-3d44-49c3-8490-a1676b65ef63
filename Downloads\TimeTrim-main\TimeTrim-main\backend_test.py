#!/usr/bin/env python3
"""
TrimTime Backend API Testing Suite
Tests all backend endpoints for the barbershop booking application
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('/app/frontend/.env')

# Get backend URL from frontend environment
BACKEND_URL = os.getenv('REACT_APP_BACKEND_URL', 'http://localhost:8001')
API_BASE_URL = f"{BACKEND_URL}/api"

class TrimTimeAPITester:
    def __init__(self):
        self.session = None
        self.test_results = []
        self.created_customer_id = None
        self.created_booking_id = None
        self.barber_ids = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_test(self, test_name: str, success: bool, details: str = "", response_data: Any = None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   Details: {details}")
        if response_data and not success:
            print(f"   Response: {response_data}")
        print()
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "response": response_data
        })
    
    async def test_root_endpoint(self):
        """Test GET /api/ - Root endpoint"""
        try:
            async with self.session.get(f"{API_BASE_URL}/") as response:
                if response.status == 200:
                    data = await response.json()
                    if "TrimTime API" in data.get("message", ""):
                        self.log_test("Root Endpoint", True, "API is responding correctly")
                        return True
                    else:
                        self.log_test("Root Endpoint", False, f"Unexpected message: {data}")
                        return False
                else:
                    self.log_test("Root Endpoint", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Root Endpoint", False, f"Connection error: {str(e)}")
            return False
    
    async def test_get_barbers(self):
        """Test GET /api/barbers - List all barbers with Indian context"""
        try:
            async with self.session.get(f"{API_BASE_URL}/barbers") as response:
                if response.status == 200:
                    barbers = await response.json()
                    if isinstance(barbers, list) and len(barbers) > 0:
                        # Store barber IDs for later tests
                        self.barber_ids = [barber["id"] for barber in barbers]
                        
                        # Verify enhanced demo barbers with Indian names are present
                        barber_names = [barber["name"] for barber in barbers]
                        expected_names = ["Raj Sharma", "David D'Souza", "Mohammed Khan"]
                        
                        # Check for Indian context
                        indian_cities = ["New Delhi", "Mumbai", "Hyderabad"]
                        found_cities = []
                        inr_pricing_found = False
                        
                        for barber in barbers:
                            location = barber.get("location", {})
                            if location.get("city") in indian_cities:
                                found_cities.append(location.get("city"))
                            
                            # Check for INR pricing in services
                            services = barber.get("services", [])
                            for service in services:
                                if service.get("price", 0) >= 150:  # INR prices should be higher
                                    inr_pricing_found = True
                                    break
                        
                        if all(name in barber_names for name in expected_names):
                            details = f"Found {len(barbers)} barbers with Indian names: {barber_names}. Cities: {found_cities}. INR pricing: {inr_pricing_found}"
                            self.log_test("Get Enhanced Barbers", True, details)
                            return True
                        else:
                            self.log_test("Get Enhanced Barbers", False, f"Missing enhanced demo barbers. Found: {barber_names}")
                            return False
                    else:
                        self.log_test("Get Enhanced Barbers", False, "No barbers returned or invalid format")
                        return False
                else:
                    self.log_test("Get Enhanced Barbers", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Get Enhanced Barbers", False, f"Error: {str(e)}")
            return False
    
    async def test_get_specific_barber(self):
        """Test GET /api/barbers/{barber_id} - Get specific barber"""
        if not self.barber_ids:
            self.log_test("Get Specific Barber", False, "No barber IDs available from previous test")
            return False
            
        try:
            barber_id = self.barber_ids[0]  # Use first barber
            async with self.session.get(f"{API_BASE_URL}/barbers/{barber_id}") as response:
                if response.status == 200:
                    barber = await response.json()
                    required_fields = ["id", "name", "email", "bio", "services", "working_hours"]
                    
                    if all(field in barber for field in required_fields):
                        services_count = len(barber.get("services", []))
                        self.log_test("Get Specific Barber", True, f"Barber details retrieved with {services_count} services")
                        return True
                    else:
                        missing_fields = [field for field in required_fields if field not in barber]
                        self.log_test("Get Specific Barber", False, f"Missing fields: {missing_fields}")
                        return False
                else:
                    self.log_test("Get Specific Barber", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Get Specific Barber", False, f"Error: {str(e)}")
            return False
    
    async def test_create_customer(self):
        """Test POST /api/customers - Create customer with Indian context"""
        customer_data = {
            "name": "Priya Sharma",
            "email": "<EMAIL>",
            "phone": "+91-98765-43210"
        }
        
        try:
            async with self.session.post(
                f"{API_BASE_URL}/customers",
                json=customer_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    customer = await response.json()
                    if customer.get("id") and customer.get("email") == customer_data["email"]:
                        self.created_customer_id = customer["id"]
                        self.log_test("Create Customer", True, f"Indian customer created with ID: {customer['id']}")
                        return True
                    else:
                        self.log_test("Create Customer", False, "Invalid customer response", customer)
                        return False
                else:
                    error_data = await response.text()
                    self.log_test("Create Customer", False, f"HTTP {response.status}", error_data)
                    return False
        except Exception as e:
            self.log_test("Create Customer", False, f"Error: {str(e)}")
            return False
    
    async def test_create_booking(self):
        """Test POST /api/bookings - Create booking"""
        if not self.created_customer_id or not self.barber_ids:
            self.log_test("Create Booking", False, "Missing customer ID or barber IDs from previous tests")
            return False
        
        # First get barber details to find a service ID
        try:
            barber_id = self.barber_ids[0]
            async with self.session.get(f"{API_BASE_URL}/barbers/{barber_id}") as response:
                if response.status != 200:
                    self.log_test("Create Booking", False, "Could not fetch barber details for service ID")
                    return False
                
                barber = await response.json()
                services = barber.get("services", [])
                if not services:
                    self.log_test("Create Booking", False, "No services available for barber")
                    return False
                
                service_id = services[0]["id"]
        except Exception as e:
            self.log_test("Create Booking", False, f"Error getting barber services: {str(e)}")
            return False
        
        # Create booking
        booking_data = {
            "customer_id": self.created_customer_id,
            "barber_id": barber_id,
            "service_id": service_id,
            "appointment_date": (datetime.now() + timedelta(days=1)).isoformat(),
            "notes": "First time customer, looking for a modern fade"
        }
        
        try:
            async with self.session.post(
                f"{API_BASE_URL}/bookings",
                json=booking_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    booking = await response.json()
                    if booking.get("id") and booking.get("total_amount"):
                        self.created_booking_id = booking["id"]
                        self.log_test("Create Booking", True, f"Booking created with ID: {booking['id']}, Amount: ${booking['total_amount']}")
                        return True
                    else:
                        self.log_test("Create Booking", False, "Invalid booking response", booking)
                        return False
                else:
                    error_data = await response.text()
                    self.log_test("Create Booking", False, f"HTTP {response.status}", error_data)
                    return False
        except Exception as e:
            self.log_test("Create Booking", False, f"Error: {str(e)}")
            return False
    
    async def test_get_bookings(self):
        """Test GET /api/bookings - Get bookings with filters"""
        if not self.created_customer_id:
            self.log_test("Get Bookings", False, "No customer ID available from previous tests")
            return False
        
        try:
            # Test getting bookings by customer ID
            async with self.session.get(f"{API_BASE_URL}/bookings?customer_id={self.created_customer_id}") as response:
                if response.status == 200:
                    bookings = await response.json()
                    if isinstance(bookings, list):
                        customer_bookings = [b for b in bookings if b.get("customer_id") == self.created_customer_id]
                        self.log_test("Get Bookings", True, f"Retrieved {len(customer_bookings)} bookings for customer")
                        return True
                    else:
                        self.log_test("Get Bookings", False, "Invalid bookings response format")
                        return False
                else:
                    self.log_test("Get Bookings", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Get Bookings", False, f"Error: {str(e)}")
            return False
    
    async def test_create_payment_session(self):
        """Test POST /api/payments/checkout/session - Create Stripe checkout session"""
        if not self.created_booking_id:
            self.log_test("Create Payment Session", False, "No booking ID available from previous tests")
            return False
        
        try:
            payment_data = {
                "booking_id": self.created_booking_id,
                "origin_url": BACKEND_URL
            }
            
            async with self.session.post(
                f"{API_BASE_URL}/payments/checkout/session",
                params=payment_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    payment_session = await response.json()
                    if payment_session.get("url") and payment_session.get("session_id"):
                        self.log_test("Create Payment Session", True, f"Payment session created with ID: {payment_session['session_id']}")
                        return True
                    else:
                        self.log_test("Create Payment Session", False, "Invalid payment session response", payment_session)
                        return False
                else:
                    error_data = await response.text()
                    self.log_test("Create Payment Session", False, f"HTTP {response.status}", error_data)
                    return False
        except Exception as e:
            self.log_test("Create Payment Session", False, f"Error: {str(e)}")
            return False
    
    async def test_create_review(self):
        """Test POST /api/reviews - Create review"""
        if not self.created_customer_id or not self.barber_ids or not self.created_booking_id:
            self.log_test("Create Review", False, "Missing required IDs from previous tests")
            return False
        
        review_data = {
            "customer_id": self.created_customer_id,
            "barber_id": self.barber_ids[0],
            "booking_id": self.created_booking_id,
            "rating": 5,
            "comment": "Excellent service! Marcus gave me exactly the cut I wanted. Very professional and friendly."
        }
        
        try:
            async with self.session.post(
                f"{API_BASE_URL}/reviews",
                json=review_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    review = await response.json()
                    if review.get("id") and review.get("rating") == 5:
                        self.log_test("Create Review", True, f"Review created with ID: {review['id']}")
                        return True
                    else:
                        self.log_test("Create Review", False, "Invalid review response", review)
                        return False
                else:
                    error_data = await response.text()
                    self.log_test("Create Review", False, f"HTTP {response.status}", error_data)
                    return False
        except Exception as e:
            self.log_test("Create Review", False, f"Error: {str(e)}")
            return False
    
    async def test_services_endpoint(self):
        """Test GET /api/services - Services information endpoint"""
        try:
            async with self.session.get(f"{API_BASE_URL}/services") as response:
                if response.status == 200:
                    services_data = await response.json()
                    
                    # Check for required structure
                    if "popular_services" in services_data and "specialty_services" in services_data:
                        popular = services_data["popular_services"]
                        specialty = services_data["specialty_services"]
                        
                        # Verify INR pricing format
                        inr_found = False
                        for service in popular:
                            if "₹" in service.get("price_range", ""):
                                inr_found = True
                                break
                        
                        if isinstance(popular, list) and isinstance(specialty, list) and inr_found:
                            self.log_test("Services Endpoint", True, f"Found {len(popular)} popular and {len(specialty)} specialty services with INR pricing")
                            return True
                        else:
                            self.log_test("Services Endpoint", False, f"Invalid services structure or missing INR pricing")
                            return False
                    else:
                        self.log_test("Services Endpoint", False, "Missing required services structure")
                        return False
                else:
                    self.log_test("Services Endpoint", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Services Endpoint", False, f"Error: {str(e)}")
            return False
    
    async def test_contact_endpoint(self):
        """Test GET /api/contact - Contact information endpoint"""
        try:
            async with self.session.get(f"{API_BASE_URL}/contact") as response:
                if response.status == 200:
                    contact_data = await response.json()
                    
                    # Check for required structure
                    required_sections = ["company", "contact", "offices", "support_hours"]
                    if all(section in contact_data for section in required_sections):
                        offices = contact_data["offices"]
                        
                        # Verify Indian offices
                        indian_cities = ["New Delhi", "Mumbai", "Bangalore"]
                        found_cities = [office.get("city") for office in offices]
                        
                        # Check for Indian phone numbers
                        indian_phone = any("+91" in contact_data["contact"].get("phone", ""))
                        
                        if any(city in found_cities for city in indian_cities) and indian_phone:
                            self.log_test("Contact Endpoint", True, f"Found Indian offices in: {found_cities} with Indian contact details")
                            return True
                        else:
                            self.log_test("Contact Endpoint", False, f"Missing Indian context. Cities: {found_cities}, Indian phone: {indian_phone}")
                            return False
                    else:
                        missing = [s for s in required_sections if s not in contact_data]
                        self.log_test("Contact Endpoint", False, f"Missing sections: {missing}")
                        return False
                else:
                    self.log_test("Contact Endpoint", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Contact Endpoint", False, f"Error: {str(e)}")
            return False
    
    async def test_barber_registration(self):
        """Test POST /api/barber-registration - Barber registration endpoint"""
        registration_data = {
            "name": "Arjun Patel",
            "shop_name": "Modern Cuts Studio",
            "email": "<EMAIL>",
            "phone": "+91-98765-12345",
            "address": "Shop 15, MG Road",
            "city": "Pune",
            "state": "Maharashtra",
            "postal_code": "411001",
            "bio": "Experienced barber specializing in modern cuts and traditional styles",
            "experience_years": 8,
            "specialties": ["Modern Cuts", "Traditional Styles", "Beard Grooming"],
            "languages": ["Hindi", "English", "Marathi"],
            "certifications": ["Professional Barber Certificate"]
        }
        
        try:
            async with self.session.post(
                f"{API_BASE_URL}/barber-registration",
                json=registration_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("message") and result.get("barber_id"):
                        self.log_test("Barber Registration", True, f"Barber registered successfully with ID: {result['barber_id']}")
                        return True
                    else:
                        self.log_test("Barber Registration", False, "Invalid registration response", result)
                        return False
                else:
                    error_data = await response.text()
                    self.log_test("Barber Registration", False, f"HTTP {response.status}", error_data)
                    return False
        except Exception as e:
            self.log_test("Barber Registration", False, f"Error: {str(e)}")
            return False
    
    async def test_queue_management(self):
        """Test GET /api/queue/{barber_id} - Queue management endpoint"""
        if not self.barber_ids:
            self.log_test("Queue Management", False, "No barber IDs available from previous tests")
            return False
        
        try:
            barber_id = self.barber_ids[0]
            async with self.session.get(f"{API_BASE_URL}/queue/{barber_id}") as response:
                if response.status == 200:
                    queue_data = await response.json()
                    
                    required_fields = ["barber_id", "queue_size", "estimated_wait_time", "next_available"]
                    if all(field in queue_data for field in required_fields):
                        self.log_test("Queue Management", True, f"Queue info: Size={queue_data['queue_size']}, Wait={queue_data['estimated_wait_time']}min")
                        return True
                    else:
                        missing = [f for f in required_fields if f not in queue_data]
                        self.log_test("Queue Management", False, f"Missing fields: {missing}")
                        return False
                else:
                    self.log_test("Queue Management", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Queue Management", False, f"Error: {str(e)}")
            return False
    
    async def test_review_response(self):
        """Test PUT /api/reviews/{review_id}/response - Review response endpoint"""
        # First create a review to respond to
        if not self.created_customer_id or not self.barber_ids or not self.created_booking_id:
            self.log_test("Review Response", False, "Missing required IDs from previous tests")
            return False
        
        # Create a review first
        review_data = {
            "customer_id": self.created_customer_id,
            "barber_id": self.barber_ids[0],
            "booking_id": self.created_booking_id,
            "rating": 4,
            "comment": "Good service, but could be faster."
        }
        
        try:
            # Create review
            async with self.session.post(
                f"{API_BASE_URL}/reviews",
                json=review_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status != 200:
                    self.log_test("Review Response", False, "Could not create review for testing response")
                    return False
                
                review = await response.json()
                review_id = review.get("id")
                
                if not review_id:
                    self.log_test("Review Response", False, "No review ID returned")
                    return False
            
            # Now test the response functionality
            response_data = {
                "response": "Thank you for your feedback! We'll work on improving our service speed."
            }
            
            async with self.session.put(
                f"{API_BASE_URL}/reviews/{review_id}/response",
                json=response_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("message"):
                        self.log_test("Review Response", True, "Barber response added successfully")
                        return True
                    else:
                        self.log_test("Review Response", False, "Invalid response", result)
                        return False
                else:
                    error_data = await response.text()
                    self.log_test("Review Response", False, f"HTTP {response.status}", error_data)
                    return False
        except Exception as e:
            self.log_test("Review Response", False, f"Error: {str(e)}")
            return False
    
    async def test_inr_currency_verification(self):
        """Test INR currency in payment transactions"""
        if not self.created_booking_id:
            self.log_test("INR Currency Verification", False, "No booking ID available from previous tests")
            return False
        
        try:
            payment_data = {
                "booking_id": self.created_booking_id,
                "origin_url": BACKEND_URL
            }
            
            async with self.session.post(
                f"{API_BASE_URL}/payments/checkout/session",
                params=payment_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    payment_session = await response.json()
                    session_id = payment_session.get("session_id")
                    
                    if session_id:
                        # Check payment status to verify currency
                        async with self.session.get(f"{API_BASE_URL}/payments/checkout/status/{session_id}") as status_response:
                            if status_response.status == 200:
                                status_data = await status_response.json()
                                currency = status_data.get("currency", "").lower()
                                
                                if currency == "inr":
                                    self.log_test("INR Currency Verification", True, f"Payment currency correctly set to INR")
                                    return True
                                else:
                                    self.log_test("INR Currency Verification", False, f"Currency is {currency}, expected INR")
                                    return False
                            else:
                                self.log_test("INR Currency Verification", False, "Could not check payment status")
                                return False
                    else:
                        self.log_test("INR Currency Verification", False, "No session ID returned")
                        return False
                else:
                    self.log_test("INR Currency Verification", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("INR Currency Verification", False, f"Error: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """Run all backend API tests"""
        print("🧪 Starting TrimTime Backend API Tests")
        print(f"🔗 Testing API at: {API_BASE_URL}")
        print("=" * 60)
        
        # Test sequence - order matters for dependent tests
        tests = [
            ("Server Connection", self.test_root_endpoint),
            ("Demo Barbers Initialization", self.test_get_barbers),
            ("Barber Details Retrieval", self.test_get_specific_barber),
            ("Customer Creation", self.test_create_customer),
            ("Booking Creation", self.test_create_booking),
            ("Booking Retrieval", self.test_get_bookings),
            ("Payment Session Creation", self.test_create_payment_session),
            ("Review Creation", self.test_create_review),
            ("Review Retrieval", self.test_get_barber_reviews),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"🔄 Running: {test_name}")
            success = await test_func()
            if success:
                passed += 1
            print()
        
        print("=" * 60)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Backend is working correctly.")
        else:
            print(f"⚠️  {total - passed} tests failed. Check the details above.")
        
        return passed, total

async def main():
    """Main test runner"""
    async with TrimTimeAPITester() as tester:
        passed, total = await tester.run_all_tests()
        return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)